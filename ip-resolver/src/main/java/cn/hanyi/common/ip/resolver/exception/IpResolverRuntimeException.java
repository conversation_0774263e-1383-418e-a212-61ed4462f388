package cn.hanyi.common.ip.resolver.exception;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class IpResolverRuntimeException extends RuntimeException {
    public IpResolverRuntimeException() {
    }

    public IpResolverRuntimeException(String message) {
        super(message);
    }

    public IpResolverRuntimeException(String message,Throwable cause) {
        super(message,cause);
    }

    public IpResolverRuntimeException(Throwable cause) {
        super(cause);
    }

    public IpResolverRuntimeException(String message,Throwable cause,boolean enableSuppression,boolean writableStackTrace) {
        super(message,cause,enableSuppression,writableStackTrace);
    }
}
