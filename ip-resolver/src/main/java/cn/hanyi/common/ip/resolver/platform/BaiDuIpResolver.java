package cn.hanyi.common.ip.resolver.platform;

import cn.hanyi.common.ip.resolver.IpResolverProperties;
import cn.hanyi.common.ip.resolver.RegionInfo;
import cn.hanyi.common.ip.resolver.dto.BaiDuMapDto;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

/**
 * 百度IP解析
 *
 * <AUTHOR>
 */
public class BaiDuIpResolver implements IpResolver {
    private String apiKey = "6FdbS34EVDAQsxtfY75v1qGEXk6fNrQV";
    private String apiSecret = "";
    private String url = "http://api.map.baidu.com/location/ip?ip=%s&ak=%s";
    private IpResolverProperties.BaiDuConfig property;

    public BaiDuIpResolver(IpResolverProperties.BaiDuConfig property) {
        this.property = property;
    }

    public BaiDuIpResolver() {
        if( this.property == null) {
            this.property = new IpResolverProperties.BaiDuConfig();
            this.property.setUrl(this.url);
            this.property.setApiKey(this.apiKey);
            this.property.setApiSecret(this.apiSecret);
        }
    }

    @Override
    public RegionInfo resolveRegion(String ip){

        //先用本地ip库匹配
        RegionInfo regionInfo = new LocalIpResolver().resolveRegion(ip);
        if(regionInfo != null && !regionInfo.getProvince().equals("未知") && !regionInfo.getCity().equals("未知")) return regionInfo;

        try {
            RestTemplate restTemplate = new RestTemplate();
            BaiDuMapDto baiDuMapDto = restTemplate.getForObject(String.format(this.property.getUrl(), ip, this.property.getApiKey()), BaiDuMapDto.class);
            if(baiDuMapDto != null && baiDuMapDto.getStatus().equals(0)){
                return  RegionInfo.builder().country("中国")
                        .province(baiDuMapDto.getContent().getAddressDetail().getProvince().replace("省", "").replace("市", ""))
                        .city(baiDuMapDto.getContent().getAddressDetail().getCity().replace("市", ""))
                        .build();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return RegionInfo.builder().country("中国").province("未知").city("未知").build();
    }

    @Override
    public RegionInfo resolveLocation(Float latitude, Float longitude) throws IOException {
        return null;
    }
}


