package cn.hanyi.common.ip.resolver.platform;

import cn.hanyi.common.ip.resolver.RegionInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * LocalIpResolver: ip2region 通过实现本地快速IP解析地理位置
 * https://github.com/lionsoul2014/ip2region
 *
 * <AUTHOR>
 */
@Getter
@Slf4j
@Setter
public class LocalIpResolver implements IpResolver {
    private Method method = null;
    private Searcher searcher;

    public LocalIpResolver() throws RuntimeException {
        try {
            byte[] bytes = LocalIpResolver.class.getResourceAsStream("/ip2region.xdb").readAllBytes();
            this.searcher = Searcher.newWithBuffer(bytes);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException("Failed to build db for ip2region: " + ex.getMessage());
        }
    }
    
    @Override
    public RegionInfo resolveRegion(String ip) {
        if (ip == null || !StringUtils.hasText(ip)) {
            return null;
        }

        try {
            String[] result = searcher.search(ip).split("\\|");
            // _城市Id|国家|区域|省份|城市|ISP_
            // 中国|0|广东|深圳|电信
            // 存在未解析到的情况 中国|0|0|0|0 使用未知来代替
//            String[] result = dataBlock.getRegion().split("\\|");
            return RegionInfo.builder()
                    .country(result[0].replace("0", "未知"))
                    .province(result[2].replace("0", "未知").replace("省", ""))
                    .city(result[3].replace("0", "未知").replace("市", ""))
                    .build();
        } catch (InvocationTargetException e) {
            log.error("Failed to resolve region: " + e.getMessage());
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            log.error("Failed to resolve region: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
        }

        return null;
    }

    @Override
    public RegionInfo resolveLocation(Float latitude, Float longitude) throws IOException {
        return null;
    }
}
