package cn.hanyi.common.ip.resolver.platform;

import cn.hanyi.common.ip.resolver.RegionInfo;
import cn.hanyi.common.ip.resolver.dto.TencentMapDto;
import org.apache.commons.lang3.NotImplementedException;
import org.geotools.data.DataStore;
import org.geotools.data.DataStoreFinder;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureSource;
import org.geotools.factory.CommonFactoryFinder;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.index.strtree.STRtree;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.filter.Filter;
import org.opengis.filter.FilterFactory2;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;



import java.io.IOException;


public class ShpResolver implements IpResolver {
    private SimpleFeatureSource featureSource;
    private GeometryFactory geometryFactory;
    private FilterFactory2 filterFactory;
    private DataStore dataStore;
    private STRtree spatialIndex;
    private boolean indexBuilt = false;
    private static final String shapefilePath = "src/main/resources/SHP/china.shp";


    /**
     * 初始化查询组件
     */
    private void initializeQueryComponents() throws Exception {
        // 1. 初始化几何和过滤器工厂
        geometryFactory = JTSFactoryFinder.getGeometryFactory();
        filterFactory = CommonFactoryFinder.getFilterFactory2();

        // 2. 加载Shapefile数据源
        File file = new File(shapefilePath);
        if (!file.exists()) {
            throw new RuntimeException("Shapefile not found: " + shapefilePath);
        }

        // 使用DataStore参数方式设置字符集，解决中文乱码问题
        Map<String, Object> params = new HashMap<>();
        params.put("url", file.toURI().toURL());
        params.put("charset", "UTF-8");

        this.dataStore = DataStoreFinder.getDataStore(params);
        if (this.dataStore == null) {
            throw new RuntimeException("无法打开 Shapefile: " + shapefilePath);
        }

        String typeName = this.dataStore.getTypeNames()[0];
        this.featureSource = this.dataStore.getFeatureSource(typeName);

        this.spatialIndex = new STRtree();
    }


    /**
     * 构建空间索引
     * 将所有要素的几何边界框加入STRtree索引中
     */
    private void buildSpatialIndex() throws Exception {
        if (indexBuilt) {
            return;
        }

        System.out.println("正在构建空间索引...");
        long startTime = System.currentTimeMillis();

        SimpleFeatureCollection features = featureSource.getFeatures();
        try (org.geotools.data.simple.SimpleFeatureIterator iterator = features.features()) {
            while (iterator.hasNext()) {
                SimpleFeature feature = iterator.next();
                Geometry geometry = (Geometry) feature.getDefaultGeometry();
                if (geometry != null) {
                    Envelope envelope = geometry.getEnvelopeInternal();
                    spatialIndex.insert(envelope, feature);
                }
            }
        }

        spatialIndex.build();
        indexBuilt = true;

        long endTime = System.currentTimeMillis();
        System.out.println("空间索引构建完成，耗时: " + (endTime - startTime) + "ms");
    }



    /**
     * 根据经纬度查询行政区划信息（使用空间索引优化）
     *
     * @param lng 经度
     * @param lat 纬度
     * @return 包含行政区划信息的SimpleFeature，如果未找到则返回null
     */
    public SimpleFeature queryByCoordinate(double lng, double lat) throws Exception {
        // 确保空间索引已构建
        buildSpatialIndex();

        // 创建查询点
        Point point = geometryFactory.createPoint(new Coordinate(lng, lat));

        // 使用空间索引进行快速预筛选
        List<SimpleFeature> candidates = spatialIndex.query(point.getEnvelopeInternal());

        // 在候选要素中进行精确的几何包含测试
        for (SimpleFeature candidate : candidates) {
            Geometry geometry = (Geometry) candidate.getDefaultGeometry();
            if (geometry != null && geometry.contains(point)) {
                return candidate;
            }
        }

        return null;
    }




    /**
     * 从SimpleFeature中提取行政区划信息
     *
     * @param feature SimpleFeature对象
     * @return 包含行政区划信息的Map
     */
    private Map<String, Object> extractAdminInfo(SimpleFeature feature) {
        Map<String, Object> adminInfo = new HashMap<>();

        // 根据实际的Shapefile字段名称提取信息
        // 这些字段名可能需要根据具体的Shapefile数据调整
        try {
            // 常见的行政区划字段名
            String[] possibleProvinceFields = {"PROVINCE", "省", "省份", "PROV_NAME", "省名"};
            String[] possibleCityFields = {"CITY", "市", "城市", "CITY_NAME", "市名"};
            String[] possibleDistrictFields = {"DISTRICT", "区", "县", "区县", "DIST_NAME", "区县名"};
            String[] possibleCodeFields = {"CODE", "ADCODE", "行政代码", "代码"};
            String[] possibleNameFields = {"NAME", "名称", "FULL_NAME", "全名"};

            // 提取省份信息
            String province = extractFieldValue(feature, possibleProvinceFields);
            if (province != null) {
                adminInfo.put("province", province);
            }

            // 提取城市信息
            String city = extractFieldValue(feature, possibleCityFields);
            if (city != null) {
                adminInfo.put("city", city);
            }

            // 提取区县信息
            String district = extractFieldValue(feature, possibleDistrictFields);
            if (district != null) {
                adminInfo.put("district", district);
            }

            // 提取行政代码
            String code = extractFieldValue(feature, possibleCodeFields);
            if (code != null) {
                adminInfo.put("code", code);
            }

            // 提取完整名称
            String name = extractFieldValue(feature, possibleNameFields);
            if (name != null) {
                adminInfo.put("name", name);
            }

            // 如果没有找到任何标准字段，则返回所有非几何属性
            if (adminInfo.isEmpty()) {
                feature.getProperties().forEach(property -> {
                    if (!(property.getValue() instanceof org.locationtech.jts.geom.Geometry)) {
                        adminInfo.put(property.getName().getLocalPart(), property.getValue());
                    }
                });
            }

        } catch (Exception e) {
            System.err.println("Error extracting admin info: " + e.getMessage());
        }

        return adminInfo;
    }

    /**
     * 从Feature中提取指定字段的值
     *
     * @param feature SimpleFeature对象
     * @param fieldNames 可能的字段名数组
     * @return 字段值，如果未找到则返回null
     */
    private String extractFieldValue(SimpleFeature feature, String[] fieldNames) {
        for (String fieldName : fieldNames) {
            try {
                Object value = feature.getAttribute(fieldName);
                if (value != null) {
                    return value.toString().trim();
                }
            } catch (Exception e) {
                // 字段不存在，继续尝试下一个
            }
        }
        return null;
    }

    @Override
    public RegionInfo resolveRegion(String ip){
        throw new NotImplementedException("unsupported ip query");
    }

    @Override
    public RegionInfo resolveLocation(Float latitude, Float longitude) throws IOException {
        try {
            initializeQueryComponents();
            SimpleFeature feature = queryByCoordinate(longitude, latitude);
            if (feature == null) {
                return null;
            }

            extractAdminInfo(feature);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return RegionInfo.builder().country("中国").province("未知").city("未知").district("未知").build();
    }

    public static void main(String[] args) {
        try {
            ShpResolver resolver = new ShpResolver();

            RegionInfo regionInfo = resolver.resolveLocation(39.904989f, 116.405285f);
            System.out.println(regionInfo);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}


