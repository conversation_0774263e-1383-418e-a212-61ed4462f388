package cn.hanyi.common.ip.resolver.platform;

import cn.hanyi.common.ip.resolver.IpResolverProperties;
import cn.hanyi.common.ip.resolver.RegionInfo;
import cn.hanyi.common.ip.resolver.dto.BaiDuMapDto;
import cn.hanyi.common.ip.resolver.dto.TencentMapDto;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

/**
 * 腾讯地图IP解析
 *
 * <AUTHOR>
 */
public class TencentIpResolver implements IpResolver {
    private String apiKey = "SMXBZ-PSQWZ-N7FXD-ZFLS6-AMZTJ-6YFWG";
    private String apiSecret = "";
    private String url = "https://apis.map.qq.com/ws/geocoder/v1/?location=%s&key=%s";
    private String urlIP = "https://apis.map.qq.com/ws/location/v1/ip?ip=%s&key=%s";
    private IpResolverProperties.TencentConfig property;

    public TencentIpResolver(IpResolverProperties.TencentConfig property) {
        this.property = property;
    }

    public TencentIpResolver() {
        if( this.property == null) {
            this.property = new IpResolverProperties.TencentConfig();
            this.property.setUrlIP(this.urlIP);
            this.property.setUrl(this.url);
            this.property.setApiKey(this.apiKey);
            this.property.setApiSecret(this.apiSecret);
        }
    }

    @Override
    public RegionInfo resolveRegion(String ip){

        try {
            RestTemplate restTemplate = new RestTemplate();
            TencentMapDto tencentMapDto = restTemplate.getForObject(String.format(this.property.getUrlIP(), ip, this.property.getApiKey()), TencentMapDto.class);
            if(tencentMapDto != null && tencentMapDto.getStatus().equals(0)){
                return  RegionInfo.builder()
                        .country(tencentMapDto.getResult().getAdInfo().getNation())
                        .province(tencentMapDto.getResult().getAdInfo().getProvince().replace("省", "").replace("市", ""))
                        .city(tencentMapDto.getResult().getAdInfo().getCity().replace("市", ""))
                        .district(tencentMapDto.getResult().getAdInfo().getDistrict())
                        .build();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return RegionInfo.builder().country("中国").province("未知").city("未知").district("未知").build();
    }

    @Override
    public RegionInfo resolveLocation(Float latitude, Float longitude) throws IOException {
        try {
            RestTemplate restTemplate = new RestTemplate();
            TencentMapDto tencentMapDto = restTemplate.getForObject(String.format(this.property.getUrl(), latitude.toString() + "," + longitude.toString(), this.property.getApiKey()), TencentMapDto.class);
            if(tencentMapDto != null && tencentMapDto.getStatus().equals(0)){
                return  RegionInfo.builder()
                        .country(tencentMapDto.getResult().getAdInfo().getNation())
                        .province(tencentMapDto.getResult().getAdInfo().getProvince().replace("省", "").replace("市", ""))
                        .city(tencentMapDto.getResult().getAdInfo().getCity().replace("市", ""))
                        .district(tencentMapDto.getResult().getAdInfo().getDistrict())
                        .build();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return RegionInfo.builder().country("中国").province("未知").city("未知").district("未知").build();
    }
}


