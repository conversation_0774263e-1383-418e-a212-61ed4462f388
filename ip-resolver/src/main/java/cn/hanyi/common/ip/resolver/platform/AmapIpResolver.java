package cn.hanyi.common.ip.resolver.platform;

import cn.hanyi.common.ip.resolver.IpResolverProperties;
import cn.hanyi.common.ip.resolver.RegionInfo;
import cn.hanyi.common.ip.resolver.dto.AmapDto;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.ArrayList;

/**
 * 高德IP解析
 *
 * <AUTHOR>
 */
public class AmapIpResolver implements IpResolver {
    private String apiKey = "881658d2e6a034ebc09c82bce16ee6c2";
    private String apiSecret = "";
    private String url = "https://restapi.amap.com/v3/ip?ip=%s&key=%s";
    private IpResolverProperties.AmapConfig property;

    public AmapIpResolver(IpResolverProperties.AmapConfig property) {
        this.property = property;
    }

    public AmapIpResolver() {
        if( this.property == null) {
            this.property = new IpResolverProperties.AmapConfig();
            this.property.setUrl(this.url);
            this.property.setApiKey(this.apiKey);
            this.property.setApiSecret(this.apiSecret);
        }
    }

    @Override
    public RegionInfo resolveRegion(String ip){

        //先用本地ip库匹配
        RegionInfo regionInfo = new LocalIpResolver().resolveRegion(ip);
        if(regionInfo != null && !regionInfo.getProvince().equals("未知") && !regionInfo.getCity().equals("未知")) return regionInfo;

        try {
            String province = "未知", city = "未知";
            RestTemplate restTemplate = new RestTemplate();
            AmapDto amapDto = restTemplate.getForObject(String.format(this.property.getUrl(), ip, this.property.getApiKey()), AmapDto.class);
            if(amapDto != null && amapDto.getStatus().equals(1) && amapDto.getInfoCode().equals("10000")){
                if(amapDto.getProvince() instanceof String) province = ((String) amapDto.getProvince()).replace("省", "").replace("市", "");
                if(amapDto.getCity() instanceof String) city = ((String) amapDto.getCity()).replace("市", "");
            }
            return  RegionInfo.builder().country("中国")
                    .province(province)
                    .city(city)
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return RegionInfo.builder().country("中国").province("未知").city("未知").build();
    }

    @Override
    public RegionInfo resolveLocation(Float latitude, Float longitude) throws IOException {
        return null;
    }
}


