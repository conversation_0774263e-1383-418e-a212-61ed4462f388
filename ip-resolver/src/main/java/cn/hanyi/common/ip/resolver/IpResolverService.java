package cn.hanyi.common.ip.resolver;

import cn.hanyi.common.ip.resolver.exception.IpResolverRuntimeException;
import cn.hanyi.common.ip.resolver.platform.AmapIpResolver;
import cn.hanyi.common.ip.resolver.platform.IpResolver;
import cn.hanyi.common.ip.resolver.platform.LocalIpResolver;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Setter
@Slf4j
public class IpResolverService {
    private Map<String, IpResolver> resolverMap = new HashMap<>();

    @Autowired
    private IpResolverProperties properties;

    public void registerResolver(String name, IpResolver resolver) {
        resolverMap.put(name, resolver);
    }

    @SneakyThrows
    public RegionInfo resolveIpToRegion(String ip) {
        String platform = properties.getDefaultPlatform();
        IpResolver resolver = resolverMap.get(platform);
        if (resolver == null) {
            throw new IpResolverRuntimeException("platform not supported");
        }
        return resolver.resolveRegion(ip);
    }

    @SneakyThrows
    public RegionInfo resolveLocationToRegion(Float latitude, Float longitude) {
        String platform = properties.getDefaultPlatform();
        IpResolver resolver = resolverMap.get(platform);
        if (resolver == null) {
            throw new IpResolverRuntimeException("platform not supported");
        }
        return resolver.resolveLocation(latitude,longitude);
    }
}
