//package cn.hanyi.common.ip.resolver.platform;
//
//import cn.hanyi.common.ip.resolver.RegionInfo;
//import lombok.SneakyThrows;
//import net.ipip.ipdb.City;
//import net.ipip.ipdb.CityInfo;
//import org.springframework.util.StringUtils;
//
//import java.io.InputStream;
//
///**
// * IPIP.NET 免费解析版本
// * https://www.ipip.net/
// *
// * <AUTHOR>
// */
//public class IpIpNetResolver implements IpResolver {
//    private City db = null;
//
//    @SneakyThrows
//    public IpIpNetResolver() {
//        InputStream stream = LocalIpResolver.class.getResourceAsStream("/ipipfree.ipdb");
//        this.db = new City(stream);
//
//    }
//
//
//    @Override
//    public RegionInfo resolveRegion(String ip) {
//        if (this.db == null) {
//            throw new RuntimeException("no db");
//        }
//
//        try {
//            String[] info = this.db.find(ip, "CN");
//            if (info.length >= 3) {
//                RegionInfo regionInfo = RegionInfo.builder()
//                        .country(info[0])
//                        .province(StringUtils.hasText(info[1]) && !info[1].equals("中国") ? info[1] : "未知")
//                        .city(StringUtils.hasText(info[2]) ? info[2] : "未知")
//                        .build();
//                return regionInfo;
//            }
//        } catch (Exception ex) {
//
//        }
//
//
//        return null;
//    }
//}
//
//
