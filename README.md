# common service
这里支持一些常见服务，主要是外部服务的封装，和业务逻辑无关。
设计的目的是做到常见服务的抽象封装，做到在应用侧配置驱动。

可以参考example.

## file-storage
实现对文件存储的封装，支持本地存储，阿里云OSS存储等
源于 [Spring File Storage](https://github.com/1171736840/spring-file-storage)

### Maven Dependency
```
<dependency>
  <groupId>cn.hanyi.common</groupId>
  <artifactId>file-storage</artifactId>
  <version>0.1-SNAPSHOT</version>
</dependency>
```

### application.yml
```
hanyi:
    common:
        file-storage:
            default-platform: default
            local:
                - platform: default
                  enable-storage: true
                  enable-access: true
                  domain: ""
                  base-path: /tmp/surveylite
                  path-patterns: /test/file/**
```

## ip-resolver
实现对IP Region解析封装，支持本地解析，高德地图等

### Maven Dependency
```
<dependency>
  <groupId>cn.hanyi.common</groupId>
  <artifactId>ip-resolver</artifactId>
  <version>0.1-SNAPSHOT</version>
</dependency>
```

### application.yml
```
hanyi:
    common:
        ip-resolver:
            default-platform: local
            local:
                algorithm: btree
            amap:
                api-key: key
                api-secret: secret
```
