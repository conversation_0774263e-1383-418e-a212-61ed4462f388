package cn.hanyi.common.file.storage.aspect;

import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.common.file.storage.recorder.FileRecorder;
import cn.hanyi.common.file.storage.UploadPretreatment;
import cn.hanyi.common.file.storage.platform.FileStorage;

/**
 * 上传切面调用链结束回调
 */
public interface UploadAspectChainCallback {
    FileInfo run(FileInfo fileInfo, UploadPretreatment pre, FileStorage fileStorage, FileRecorder fileRecorder);
}
