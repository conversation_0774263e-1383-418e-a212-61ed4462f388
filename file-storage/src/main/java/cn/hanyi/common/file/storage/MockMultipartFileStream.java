/*
 * Copyright 2002-2018 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.hanyi.common.file.storage;

import cn.hutool.core.io.FileUtil;
import lombok.Getter;
import lombok.SneakyThrows;
import org.springframework.lang.Nullable;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * 一个模拟 MultipartFile 的类
 */
@Getter
public class MockMultipartFileStream implements MultipartFile {

    /**
     * 文件名
     */
    private final String name;

    /**
     * 原始文件名
     */
    private final String originalFilename;

    /**
     * 内容类型
     */
    @Nullable
    private final String contentType;

    /**
     * 文件内容
     */
    @Nullable
    private final InputStream inputStream;



    public MockMultipartFileStream(String name, InputStream in) {
        this(name,"",null,in);
    }

    public MockMultipartFileStream(@Nullable String name, @Nullable String originalFilename, @Nullable String contentType, @Nullable InputStream in) {
        this.name = (name != null ? name : "");
        this.originalFilename = (originalFilename != null ? originalFilename : "");
        this.contentType = contentType;
        this.inputStream = (in != null ? in : new ByteArrayInputStream(new byte[0]));
    }


    @Override
    public boolean isEmpty() {
        return false;
    }

    @Override
    public long getSize() {
        return 0;
    }

    @Override
    public byte[] getBytes() throws IOException {
        return new byte[0];
    }

    @Override
    public void transferTo(File dest) throws IOException {
        FileUtil.writeBytes(inputStream.readAllBytes(),dest);
    }

}
