library("cli")
library("haven")
library("httr")

csvToSav <- function(url, uploadUrl, token) {
  path <- hash_md5(url)
  name <- URLdecode(basename(url))
  if (!file.exists(path)) {
    dir.create(path)
  }
  setwd(path)
  zipFile <- paste0(path, ".zip")
  download.file(url, zipFile)
  unzip(zipFile)
  file.remove(zipFile)
  files <- list.files(pattern = ".csv")
  for (file in files) {
    fileName <- basename(file)
    fileName <- paste0(substring(fileName, 1, nchar(fileName) - 4), ".sav")
    csvFile <- read.csv(file)
    names <- names(csvFile)
    for (i in seq_along(names)) {
      column <- paste0("column", i)
      label <- names[i]
      names(csvFile)[i] <- column
      attr(csvFile[[column]], "label") <- label
    }
    write_sav(csvFile, fileName)
  }
  name <- paste0(substring(name, 1, nchar(name) - 4), "-sav.zip")
  zip(name, list.files(pattern = ".sav"))
  response <- POST(uploadUrl, body = list(file = upload_file(name), useFileName = "true", token = token))
  newUrl <- content(response)["data"]$data
  setwd("..")
  unlink(path, recursive = TRUE, force = TRUE)
  newUrl
}