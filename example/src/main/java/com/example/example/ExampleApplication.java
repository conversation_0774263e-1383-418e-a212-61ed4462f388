package com.example.example;

import cn.hanyi.common.ip.resolver.IpResolverService;
import cn.hanyi.common.ip.resolver.RegionInfo;
import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.common.file.storage.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

@SpringBootApplication(scanBasePackages = {"cn.hanyi"})
public class ExampleApplication {

    @RestController
    public class FileController {
        @Autowired
        private FileStorageService storageService;

        @RequestMapping(value="/files", method = RequestMethod.POST)
        public String upload(@RequestParam("file") MultipartFile file)
                throws IOException {

            FileInfo fileInfo = storageService.of(file)
                    .setObjectId("0")
                    .upload();

            return fileInfo.getUrl();
        }
    }

    @RestController
    public class IpController {
        @Autowired
        private IpResolverService ipResolverService;

        @PostMapping(value="/ip")
        public RegionInfo resolve(@RequestBody Map<String, String> body) {
            String ip = body.get("ip");
            return ipResolverService.resolveIpToRegion(ip);
        }
    }

    public static void main(String[] args) {
        SpringApplication.run(ExampleApplication.class, args);
    }

}
